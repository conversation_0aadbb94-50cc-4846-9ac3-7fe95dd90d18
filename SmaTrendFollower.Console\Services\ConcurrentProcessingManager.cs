using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Console.Services;

/// <summary>
/// Advanced concurrent processing manager that optimizes parallel operations
/// with intelligent batching, adaptive rate limiting, and performance monitoring
/// </summary>
public class ConcurrentProcessingManager : IDisposable
{
    private readonly ILogger<ConcurrentProcessingManager> _logger;
    private readonly ConcurrentProcessingConfiguration _config;
    
    // Adaptive rate limiting
    private readonly SemaphoreSlim _globalSemaphore;
    private readonly ConcurrentDictionary<string, SemaphoreSlim> _providerSemaphores = new();
    
    // Performance tracking
    private readonly ConcurrentDictionary<string, PerformanceTracker> _performanceTrackers = new();
    private readonly Timer _adaptiveTimer;
    
    // Circuit breaker for overload protection
    private volatile bool _isOverloaded = false;
    private DateTime _lastOverloadCheck = DateTime.UtcNow;

    public ConcurrentProcessingManager(
        ILogger<ConcurrentProcessingManager> logger,
        IOptions<ConcurrentProcessingConfiguration> config)
    {
        _logger = logger;
        _config = config.Value;
        
        _globalSemaphore = new SemaphoreSlim(_config.GlobalMaxConcurrency, _config.GlobalMaxConcurrency);
        
        // Initialize adaptive timer
        _adaptiveTimer = new Timer(AdaptiveAdjustment, null, 
            _config.AdaptiveAdjustmentInterval, _config.AdaptiveAdjustmentInterval);
    }

    /// <summary>
    /// Process items in optimized batches with adaptive concurrency control
    /// </summary>
    public async Task<List<TResult>> ProcessInBatchesAsync<TItem, TResult>(
        IEnumerable<TItem> items,
        Func<TItem, CancellationToken, Task<TResult?>> processor,
        string operationType,
        CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();
        var itemList = items.ToList();
        var results = new ConcurrentBag<TResult>();
        var processedCount = 0;
        var errorCount = 0;

        try
        {
            // Check for overload condition
            if (_isOverloaded)
            {
                _logger.LogWarning("⚠️ System overloaded, reducing batch size for {Operation}", operationType);
            }

            // Create optimized batches
            var batches = CreateAdaptiveBatches(itemList, operationType);
            var tracker = GetOrCreateTracker(operationType);

            _logger.LogInformation("🚀 Processing {Items} items in {Batches} batches for {Operation}", 
                itemList.Count, batches.Count, operationType);

            // Process batches with adaptive concurrency
            var batchTasks = batches.Select(async (batch, batchIndex) =>
            {
                var batchResult = await ProcessBatchWithAdaptiveControl(batch, processor, operationType, batchIndex,
                    results, cancellationToken);
                Interlocked.Add(ref processedCount, batchResult.processedCount);
                Interlocked.Add(ref errorCount, batchResult.errorCount);
            });

            await Task.WhenAll(batchTasks);

            stopwatch.Stop();
            
            // Update performance metrics
            tracker.RecordOperation(stopwatch.Elapsed, itemList.Count, processedCount, errorCount);
            
            var finalResults = results.Where(r => r != null).ToList();
            LogOperationResults(operationType, stopwatch.Elapsed, itemList.Count, processedCount, errorCount, finalResults.Count);
            
            return finalResults;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Batch processing failed for {Operation} after {Elapsed}ms", 
                operationType, stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    private async Task<(int processedCount, int errorCount)> ProcessBatchWithAdaptiveControl<TItem, TResult>(
        List<TItem> batch,
        Func<TItem, CancellationToken, Task<TResult?>> processor,
        string operationType,
        int batchIndex,
        ConcurrentBag<TResult> results,
        CancellationToken cancellationToken)
    {
        var batchStopwatch = Stopwatch.StartNew();
        var batchSemaphore = GetProviderSemaphore(operationType);
        var processedCount = 0;
        var errorCount = 0;

        await batchSemaphore.WaitAsync(cancellationToken);
        try
        {
            // Process items in batch with controlled parallelism
            var itemTasks = batch.Select(async item =>
            {
                await _globalSemaphore.WaitAsync(cancellationToken);
                try
                {
                    var result = await processor(item, cancellationToken);
                    if (result != null)
                    {
                        results.Add(result);
                    }
                    Interlocked.Increment(ref processedCount);
                }
                catch (Exception ex)
                {
                    Interlocked.Increment(ref errorCount);
                    _logger.LogDebug(ex, "Item processing failed in batch {BatchIndex} for {Operation}", batchIndex, operationType);
                }
                finally
                {
                    _globalSemaphore.Release();
                }
            });

            await Task.WhenAll(itemTasks);

            batchStopwatch.Stop();

            // Log slow batches
            if (batchStopwatch.ElapsedMilliseconds > _config.SlowBatchThresholdMs)
            {
                _logger.LogWarning("⚠️ Slow batch {BatchIndex} for {Operation}: {Count} items in {Elapsed}ms",
                    batchIndex, operationType, batch.Count, batchStopwatch.ElapsedMilliseconds);
            }
        }
        finally
        {
            batchSemaphore.Release();
        }

        return (processedCount, errorCount);
    }

    private List<List<TItem>> CreateAdaptiveBatches<TItem>(List<TItem> items, string operationType)
    {
        var tracker = GetOrCreateTracker(operationType);
        var optimalBatchSize = CalculateOptimalBatchSize(items.Count, tracker);
        
        // Adjust for overload conditions
        if (_isOverloaded)
        {
            optimalBatchSize = Math.Max(1, optimalBatchSize / 2);
        }

        var batches = new List<List<TItem>>();
        for (int i = 0; i < items.Count; i += optimalBatchSize)
        {
            var batch = items.Skip(i).Take(optimalBatchSize).ToList();
            batches.Add(batch);
        }

        return batches;
    }

    private int CalculateOptimalBatchSize(int totalItems, PerformanceTracker tracker)
    {
        // Base batch size from configuration
        var baseBatchSize = _config.DefaultBatchSize;
        
        // Adjust based on historical performance
        if (tracker.AverageLatencyMs > _config.HighLatencyThresholdMs)
        {
            baseBatchSize = Math.Max(1, baseBatchSize / 2); // Reduce batch size for slow operations
        }
        else if (tracker.AverageLatencyMs < _config.LowLatencyThresholdMs && tracker.ErrorRate < 0.05)
        {
            baseBatchSize = Math.Min(_config.MaxBatchSize, baseBatchSize * 2); // Increase for fast operations
        }

        // Ensure reasonable bounds
        return Math.Max(1, Math.Min(totalItems, baseBatchSize));
    }

    private SemaphoreSlim GetProviderSemaphore(string operationType)
    {
        return _providerSemaphores.GetOrAdd(operationType, _ => 
            new SemaphoreSlim(_config.ProviderMaxConcurrency, _config.ProviderMaxConcurrency));
    }

    private PerformanceTracker GetOrCreateTracker(string operationType)
    {
        return _performanceTrackers.GetOrAdd(operationType, _ => new PerformanceTracker());
    }

    private void AdaptiveAdjustment(object? state)
    {
        try
        {
            // Check for overload conditions
            CheckOverloadConditions();
            
            // Adjust semaphore limits based on performance
            AdjustSemaphoreLimits();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error during adaptive adjustment");
        }
    }

    private void CheckOverloadConditions()
    {
        var now = DateTime.UtcNow;
        if (now - _lastOverloadCheck < TimeSpan.FromSeconds(30))
            return;

        _lastOverloadCheck = now;

        // Check global performance metrics
        var totalErrorRate = _performanceTrackers.Values.Average(t => t.ErrorRate);
        var avgLatency = _performanceTrackers.Values.Average(t => t.AverageLatencyMs);

        var wasOverloaded = _isOverloaded;
        _isOverloaded = totalErrorRate > 0.15 || avgLatency > _config.OverloadLatencyThresholdMs;

        if (_isOverloaded != wasOverloaded)
        {
            _logger.LogWarning("🚨 System overload status changed: {Status} (ErrorRate: {ErrorRate:P}, AvgLatency: {Latency}ms)",
                _isOverloaded ? "OVERLOADED" : "NORMAL", totalErrorRate, avgLatency);
        }
    }

    private void AdjustSemaphoreLimits()
    {
        // Implementation for dynamic semaphore adjustment would go here
        // This is a placeholder for future enhancement
    }

    private void LogOperationResults(string operationType, TimeSpan elapsed, int total, int processed, int errors, int results)
    {
        var successRate = total > 0 ? (double)processed / total * 100 : 0;
        var errorRate = total > 0 ? (double)errors / total * 100 : 0;
        var avgTimePerItem = total > 0 ? elapsed.TotalMilliseconds / total : 0;

        _logger.LogInformation(
            "📊 {Operation} completed: {Elapsed}ms total, {AvgPerItem:F1}ms/item, " +
            "{Processed}/{Total} processed ({SuccessRate:F1}%), {Errors} errors ({ErrorRate:F1}%), {Results} results",
            operationType, elapsed.TotalMilliseconds, avgTimePerItem, processed, total, successRate, errors, errorRate, results);

        // Log performance warning if needed
        if (avgTimePerItem > _config.SlowItemThresholdMs)
        {
            _logger.LogWarning("⚠️ {Operation} performance degraded: {AvgPerItem:F1}ms/item exceeds threshold {Threshold}ms",
                operationType, avgTimePerItem, _config.SlowItemThresholdMs);
        }
    }

    public void Dispose()
    {
        _adaptiveTimer?.Dispose();
        _globalSemaphore?.Dispose();
        
        foreach (var semaphore in _providerSemaphores.Values)
        {
            semaphore?.Dispose();
        }
    }
}

/// <summary>
/// Performance tracking for operations
/// </summary>
public class PerformanceTracker
{
    private readonly Queue<OperationMetric> _recentOperations = new();
    private readonly object _lock = new();

    public double AverageLatencyMs { get; private set; }
    public double ErrorRate { get; private set; }
    public int TotalOperations { get; private set; }

    public void RecordOperation(TimeSpan duration, int totalItems, int processedItems, int errors)
    {
        lock (_lock)
        {
            var metric = new OperationMetric
            {
                Duration = duration,
                TotalItems = totalItems,
                ProcessedItems = processedItems,
                Errors = errors,
                Timestamp = DateTime.UtcNow
            };

            _recentOperations.Enqueue(metric);
            
            // Keep only recent operations (last 100)
            while (_recentOperations.Count > 100)
            {
                _recentOperations.Dequeue();
            }

            // Recalculate metrics
            if (_recentOperations.Count > 0)
            {
                AverageLatencyMs = _recentOperations.Average(m => m.Duration.TotalMilliseconds);
                ErrorRate = _recentOperations.Sum(m => m.Errors) / (double)_recentOperations.Sum(m => m.TotalItems);
                TotalOperations = _recentOperations.Sum(m => m.TotalItems);
            }
        }
    }
}

public class OperationMetric
{
    public TimeSpan Duration { get; set; }
    public int TotalItems { get; set; }
    public int ProcessedItems { get; set; }
    public int Errors { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Configuration for concurrent processing optimization
/// </summary>
public class ConcurrentProcessingConfiguration
{
    public int GlobalMaxConcurrency { get; set; } = 20;
    public int ProviderMaxConcurrency { get; set; } = 5;
    public int DefaultBatchSize { get; set; } = 10;
    public int MaxBatchSize { get; set; } = 50;
    public int SlowBatchThresholdMs { get; set; } = 30000;
    public int SlowItemThresholdMs { get; set; } = 3000;
    public int HighLatencyThresholdMs { get; set; } = 5000;
    public int LowLatencyThresholdMs { get; set; } = 1000;
    public int OverloadLatencyThresholdMs { get; set; } = 10000;
    public TimeSpan AdaptiveAdjustmentInterval { get; set; } = TimeSpan.FromMinutes(2);
}
