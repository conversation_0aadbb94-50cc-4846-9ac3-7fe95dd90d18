using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Data;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service to optimize database performance for SmaTrendFollower.
/// Fixes critical performance issues that cause universe refresh to hang.
/// </summary>
public interface IDatabaseOptimizationService
{
    Task OptimizeDatabaseAsync();
    Task VerifyOptimizationsAsync();
    Task<DatabasePerformanceReport> GetPerformanceReportAsync();
}

public class DatabaseOptimizationService : IDatabaseOptimizationService
{
    private readonly IDbContextFactory<StockBarCacheDbContext> _stockContextFactory;
    private readonly IDbContextFactory<IndexCacheDbContext> _indexContextFactory;
    private readonly ILogger<DatabaseOptimizationService> _logger;

    public DatabaseOptimizationService(
        IDbContextFactory<StockBarCacheDbContext> stockContextFactory,
        IDbContextFactory<IndexCacheDbContext> indexContextFactory,
        ILogger<DatabaseOptimizationService> logger)
    {
        _stockContextFactory = stockContextFactory;
        _indexContextFactory = indexContextFactory;
        _logger = logger;
    }

    public async Task OptimizeDatabaseAsync()
    {
        _logger.LogInformation("🚀 Starting database optimization to fix universe refresh hanging...");
        var stopwatch = Stopwatch.StartNew();

        try
        {
            await CreateCriticalIndexesAsync();
            await CreatePerformanceIndexesAsync();
            await ApplyPostgreSQLOptimizationsAsync();
            await UpdateTableStatisticsAsync();

            stopwatch.Stop();
            _logger.LogInformation("✅ Database optimization completed successfully in {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "❌ Database optimization failed after {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);
            throw;
        }
    }

    private async Task CreateCriticalIndexesAsync()
    {
        _logger.LogInformation("🔧 Creating critical indexes to fix UPDATE performance...");

        using var context = await _stockContextFactory.CreateDbContextAsync();

        // CRITICAL: Index on Id column for CachedStockBars (fixes 7+ second UPDATEs)
        await ExecuteSqlSafelyAsync(context, 
            @"CREATE INDEX CONCURRENTLY IF NOT EXISTS ""IX_CachedStockBars_Id"" 
              ON ""CachedStockBars"" (""Id"")",
            "CachedStockBars.Id index");

        // CRITICAL: Index on Id column for CachedIndexBars
        await ExecuteSqlSafelyAsync(context,
            @"CREATE INDEX CONCURRENTLY IF NOT EXISTS ""IX_CachedIndexBars_Id"" 
              ON ""CachedIndexBars"" (""Id"")",
            "CachedIndexBars.Id index");

        _logger.LogInformation("✅ Critical indexes created successfully");
    }

    private async Task CreatePerformanceIndexesAsync()
    {
        _logger.LogInformation("🔧 Creating additional performance indexes...");

        using var context = await _stockContextFactory.CreateDbContextAsync();

        // Composite index for cache timestamp updates
        await ExecuteSqlSafelyAsync(context,
            @"CREATE INDEX CONCURRENTLY IF NOT EXISTS ""IX_CachedStockBars_Symbol_TimeFrame_CachedAt"" 
              ON ""CachedStockBars"" (""Symbol"", ""TimeFrame"", ""CachedAt"")",
            "Symbol_TimeFrame_CachedAt composite index");

        // Index for cache cleanup operations
        await ExecuteSqlSafelyAsync(context,
            @"CREATE INDEX CONCURRENTLY IF NOT EXISTS ""IX_CachedStockBars_CachedAt_Symbol"" 
              ON ""CachedStockBars"" (""CachedAt"", ""Symbol"")",
            "CachedAt_Symbol index");

        // Index for metadata lookups
        await ExecuteSqlSafelyAsync(context,
            @"CREATE INDEX CONCURRENTLY IF NOT EXISTS ""IX_StockCacheMetadata_LastUpdated"" 
              ON ""StockCacheMetadata"" (""LastUpdated"")",
            "StockCacheMetadata.LastUpdated index");

        _logger.LogInformation("✅ Performance indexes created successfully");
    }

    private async Task ApplyPostgreSQLOptimizationsAsync()
    {
        _logger.LogInformation("🔧 Applying PostgreSQL performance optimizations...");

        using var context = await _stockContextFactory.CreateDbContextAsync();

        // Apply session-level optimizations (safer than ALTER SYSTEM)
        var optimizations = new[]
        {
            ("work_mem", "256MB", "Increase work memory for complex queries"),
            ("random_page_cost", "1.1", "Optimize for SSD storage"),
            ("effective_cache_size", "1GB", "Set effective cache size")
        };

        foreach (var (setting, value, description) in optimizations)
        {
            await ExecuteSqlSafelyAsync(context,
                $"SET {setting} = '{value}'",
                $"PostgreSQL setting: {description}");
        }

        // Set table-specific optimizations
        await ExecuteSqlSafelyAsync(context,
            @"ALTER TABLE ""CachedStockBars"" SET (fillfactor = 90)",
            "CachedStockBars fill factor");

        await ExecuteSqlSafelyAsync(context,
            @"ALTER TABLE ""StockCacheMetadata"" SET (fillfactor = 90)",
            "StockCacheMetadata fill factor");

        _logger.LogInformation("✅ PostgreSQL optimizations applied successfully");
    }

    private async Task UpdateTableStatisticsAsync()
    {
        _logger.LogInformation("🔧 Updating table statistics for better query planning...");

        using var context = await _stockContextFactory.CreateDbContextAsync();

        var tables = new[] { "CachedStockBars", "StockCacheMetadata", "CachedIndexBars", "CacheMetadata" };

        foreach (var table in tables)
        {
            await ExecuteSqlSafelyAsync(context,
                $@"ANALYZE ""{table}""",
                $"Statistics for {table}");
        }

        _logger.LogInformation("✅ Table statistics updated successfully");
    }

    public async Task VerifyOptimizationsAsync()
    {
        _logger.LogInformation("🔍 Verifying database optimizations...");

        using var context = await _stockContextFactory.CreateDbContextAsync();

        // Check if critical indexes exist
        var indexQuery = @"
            SELECT indexname, tablename 
            FROM pg_indexes 
            WHERE tablename IN ('CachedStockBars', 'StockCacheMetadata', 'CachedIndexBars')
            AND indexname LIKE 'IX_%'
            ORDER BY tablename, indexname";

        // Use raw SQL query with proper EF Core method
        var indexes = new List<IndexInfo>();
        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = indexQuery;
        await context.Database.OpenConnectionAsync();
        using var reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            indexes.Add(new IndexInfo
            {
                IndexName = reader.GetString(0), // indexname
                TableName = reader.GetString(1)  // tablename
            });
        }

        _logger.LogInformation("📊 Found {IndexCount} indexes:", indexes.Count);
        foreach (var index in indexes)
        {
            _logger.LogInformation("  ✅ {TableName}.{IndexName}", index.TableName, index.IndexName);
        }

        // Verify critical indexes exist
        var criticalIndexes = new[] { "IX_CachedStockBars_Id", "IX_CachedIndexBars_Id" };
        var missingIndexes = criticalIndexes.Where(idx => !indexes.Any(i => i.IndexName == idx)).ToList();

        if (missingIndexes.Any())
        {
            _logger.LogWarning("⚠️ Missing critical indexes: {MissingIndexes}", string.Join(", ", missingIndexes));
        }
        else
        {
            _logger.LogInformation("✅ All critical indexes are present");
        }
    }

    public async Task<DatabasePerformanceReport> GetPerformanceReportAsync()
    {
        using var context = await _stockContextFactory.CreateDbContextAsync();

        // Get table sizes and statistics
        var sizeQuery = @"
            SELECT
                relname as tablename,
                pg_size_pretty(pg_total_relation_size(schemaname||'.'||relname)) as size,
                n_tup_ins as inserts,
                n_tup_upd as updates,
                n_tup_del as deletes
            FROM pg_stat_user_tables
            WHERE relname = 'CachedStockBars'
            ORDER BY pg_total_relation_size(schemaname||'.'||relname) DESC";

        // Use raw SQL query with proper EF Core method
        var tableStats = new List<TableStatistics>();
        using var command = context.Database.GetDbConnection().CreateCommand();
        command.CommandText = sizeQuery;
        await context.Database.OpenConnectionAsync();
        using var reader = await command.ExecuteReaderAsync();
        while (await reader.ReadAsync())
        {
            tableStats.Add(new TableStatistics
            {
                TableName = reader.GetString(0), // tablename
                Size = reader.GetString(1),      // size
                Inserts = reader.GetInt64(2),    // inserts
                Updates = reader.GetInt64(3),    // updates
                Deletes = reader.GetInt64(4)     // deletes
            });
        }

        // Test UPDATE performance
        var updatePerformance = await TestUpdatePerformanceAsync(context);

        return new DatabasePerformanceReport
        {
            TableStatistics = tableStats,
            UpdatePerformanceMs = updatePerformance,
            OptimizationStatus = updatePerformance < 100 ? "Optimized" : "Needs Optimization",
            Timestamp = DateTime.UtcNow
        };
    }

    private async Task<long> TestUpdatePerformanceAsync(StockBarCacheDbContext context)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();

            // Test a simple UPDATE operation to measure performance
            await context.Database.ExecuteSqlRawAsync(
                @"UPDATE ""StockCacheMetadata"" 
                  SET ""LastUpdated"" = NOW() 
                  WHERE ""Symbol"" = 'TEST_PERFORMANCE_SYMBOL'");

            stopwatch.Stop();
            return stopwatch.ElapsedMilliseconds;
        }
        catch
        {
            return -1; // Indicates test failed
        }
    }

    private async Task ExecuteSqlSafelyAsync(DbContext context, string sql, string description)
    {
        try
        {
            var stopwatch = Stopwatch.StartNew();
            await context.Database.ExecuteSqlRawAsync(sql);
            stopwatch.Stop();

            _logger.LogDebug("✅ {Description} completed in {ElapsedMs}ms", description, stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "⚠️ {Description} failed (may already exist): {Error}", description, ex.Message);
        }
    }
}

// Data models for verification queries
public class IndexInfo
{
    public string IndexName { get; set; } = string.Empty;
    public string TableName { get; set; } = string.Empty;
}

public class TableStatistics
{
    public string TableName { get; set; } = string.Empty;
    public string Size { get; set; } = string.Empty;
    public long Inserts { get; set; }
    public long Updates { get; set; }
    public long Deletes { get; set; }
}

public class DatabasePerformanceReport
{
    public List<TableStatistics> TableStatistics { get; set; } = new();
    public long UpdatePerformanceMs { get; set; }
    public string OptimizationStatus { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}
