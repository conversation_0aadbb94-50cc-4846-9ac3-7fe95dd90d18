using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System;
using System.Net.Http;
using System.Threading.Tasks;

namespace SmaTrendFollower.Console;

/// <summary>
/// Test program to directly test Polygon API responses
/// </summary>
public static class TestPolygonApiDirect
{
    public static async Task RunAsync(IServiceProvider serviceProvider)
    {
        System.Console.WriteLine("🔍 Testing Polygon API Direct Responses...\n");

        var logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        var polygonFactory = serviceProvider.GetRequiredService<IPolygonClientFactory>();

        try
        {
            var httpClient = polygonFactory.CreateClient();
            var apiKey = Environment.GetEnvironmentVariable("POLYGON_API_KEY") ?? 
                        Environment.GetEnvironmentVariable("POLY_API_KEY");

            System.Console.WriteLine($"🔑 API Key: {apiKey?.Substring(0, 10)}...");
            System.Console.WriteLine($"🌐 Base URL: {httpClient.BaseAddress}");

            // Test 1: Direct API call for SPY data
            System.Console.WriteLine("\n📊 Test 1: Direct SPY API Call");
            System.Console.WriteLine("===============================");

            var testUrls = new[]
            {
                "/v2/aggs/ticker/SPY/range/1/day/2025-01-02/2025-07-24?adjusted=true&sort=asc&limit=50000",
                "/v2/aggs/ticker/SPY/range/1/day/2025-01-06/2025-07-24?adjusted=true&sort=asc&limit=50000",
                "/v2/aggs/ticker/SPY/range/1/day/2024-07-01/2024-07-24?adjusted=true&sort=asc&limit=50000",
                "/v2/aggs/ticker/SPY/range/1/day/2024-01-01/2024-07-24?adjusted=true&sort=asc&limit=50000"
            };

            foreach (var testUrl in testUrls)
            {
                System.Console.WriteLine($"\n🧪 Testing URL: {testUrl}");
                
                try
                {
                    var urlWithApiKey = polygonFactory.AddApiKeyToUrl(testUrl);
                    var response = await httpClient.GetAsync(urlWithApiKey);
                    
                    System.Console.WriteLine($"   Status: {response.StatusCode}");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        System.Console.WriteLine($"   Content Length: {content.Length} characters");
                        
                        // Show first 500 characters of response
                        var preview = content.Length > 500 ? content.Substring(0, 500) + "..." : content;
                        System.Console.WriteLine($"   Response Preview: {preview}");
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        System.Console.WriteLine($"   ❌ Error Response: {errorContent}");
                    }
                }
                catch (Exception ex)
                {
                    System.Console.WriteLine($"   ❌ Exception: {ex.Message}");
                    logger.LogError(ex, "Error testing URL: {Url}", testUrl);
                }
            }

            // Test 2: Test other symbols to see if it's SPY-specific
            System.Console.WriteLine("\n\n📊 Test 2: Other Symbols Test");
            System.Console.WriteLine("==============================");

            var testSymbols = new[] { "AAPL", "MSFT", "QQQ", "IWM", "VTI" };
            var testDate = "2025-01-02/2025-07-24";

            foreach (var symbol in testSymbols)
            {
                System.Console.WriteLine($"\n🧪 Testing symbol: {symbol}");
                
                try
                {
                    var url = $"/v2/aggs/ticker/{symbol}/range/1/day/{testDate}?adjusted=true&sort=asc&limit=50000";
                    var urlWithApiKey = polygonFactory.AddApiKeyToUrl(url);
                    var response = await httpClient.GetAsync(urlWithApiKey);
                    
                    System.Console.WriteLine($"   Status: {response.StatusCode}");
                    
                    if (response.IsSuccessStatusCode)
                    {
                        var content = await response.Content.ReadAsStringAsync();
                        
                        // Try to parse and count results
                        if (content.Contains("\"results\""))
                        {
                            var resultsStart = content.IndexOf("\"results\":[") + 11;
                            var resultsEnd = content.IndexOf("]", resultsStart);
                            if (resultsEnd > resultsStart)
                            {
                                var resultsSection = content.Substring(resultsStart, resultsEnd - resultsStart);
                                var barCount = resultsSection.Split('{').Length - 1;
                                System.Console.WriteLine($"   ✅ Found ~{barCount} bars");
                            }
                        }
                        else
                        {
                            System.Console.WriteLine($"   ⚠️  No results array found");
                        }
                    }
                    else
                    {
                        var errorContent = await response.Content.ReadAsStringAsync();
                        System.Console.WriteLine($"   ❌ Error: {errorContent}");
                    }
                }
                catch (Exception ex)
                {
                    System.Console.WriteLine($"   ❌ Exception: {ex.Message}");
                }
            }

            // Test 3: Check subscription info
            System.Console.WriteLine("\n\n📊 Test 3: Subscription Info");
            System.Console.WriteLine("=============================");

            try
            {
                var url = "/v1/marketstatus/now";
                var urlWithApiKey = polygonFactory.AddApiKeyToUrl(url);
                var response = await httpClient.GetAsync(urlWithApiKey);
                
                System.Console.WriteLine($"Market Status API: {response.StatusCode}");
                
                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    System.Console.WriteLine($"Market Status: {content}");
                }
            }
            catch (Exception ex)
            {
                System.Console.WriteLine($"❌ Market status check failed: {ex.Message}");
            }

            System.Console.WriteLine("\n🎯 Polygon API Direct Test Complete!");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Fatal error during Polygon API test: {ex.Message}");
            logger.LogError(ex, "Fatal error during Polygon API direct test");
        }
    }
}
