using Microsoft.EntityFrameworkCore;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for performance
/// </summary>
public interface IDatabaseConfigurationService
{
    /// <summary>
    /// Configures PostgreSQL DbContext options with optimized settings
    /// </summary>
    /// <param name="options">DbContext options builder</param>
    /// <param name="connectionString">PostgreSQL connection string</param>
    void ConfigurePostgreSQL(DbContextOptionsBuilder options, string connectionString);

    /// <summary>
    /// Gets optimized PostgreSQL connection string with performance settings
    /// </summary>
    /// <param name="baseConnectionString">Base PostgreSQL connection string</param>
    /// <returns>Optimized connection string</returns>
    string GetOptimizedPostgreSQLConnectionString(string baseConnectionString);

    /// <summary>
    /// Optimizes PostgreSQL database with VACUUM, ANALYZE, and other maintenance operations
    /// </summary>
    /// <param name="connectionString">PostgreSQL connection string</param>
    Task OptimizePostgreSQLAsync(string connectionString);
}

/// <summary>
/// Static helper methods for database configuration
/// </summary>
public static class DatabaseConfigurationExtensions
{
    /// <summary>
    /// Creates an optimized PostgreSQL connection string builder with high-concurrency settings
    /// This is the standard configuration used throughout the trading system
    /// </summary>
    /// <param name="baseConnectionString">Base connection string</param>
    /// <returns>Optimized connection string builder</returns>
    public static Npgsql.NpgsqlConnectionStringBuilder CreateOptimizedBuilder(string baseConnectionString)
    {
        return new Npgsql.NpgsqlConnectionStringBuilder(baseConnectionString)
        {
            MaxPoolSize = 200, // Increased from default 100 to handle high concurrency
            MinPoolSize = 20,  // Increased from default 1 to maintain warm connections
            Pooling = true,
            ConnectionIdleLifetime = 300, // 5 minutes
            CommandTimeout = 45, // Increased from 30 to handle network delays
            Timeout = 45,        // Increased from 30 to handle network delays
            Multiplexing = true,
            ReadBufferSize = 8192,
            WriteBufferSize = 8192,
            // Network resilience settings to prevent "Exception while reading from stream"
            TcpKeepAlive = true,
            TcpKeepAliveTime = 60,     // Send keepalive every 60 seconds
            TcpKeepAliveInterval = 10,  // Retry every 10 seconds
            CancellationTimeout = 45000 // 45 seconds in milliseconds
        };
    }
}
