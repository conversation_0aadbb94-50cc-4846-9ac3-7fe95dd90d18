{
  "Redis": {
    "ConnectionString": "192.168.1.168:6379",
    "UniverseCacheConnection": "192.168.1.168:6379"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "SmaTrendFollower": "Information"
    }
  },
  "Alpaca": {
    "KeyId": "AKGBPW5HD8LVI5C6NJUJ",
    "SecretKey": "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM",
    "Environment": "live"
  },
  "Polygon": {
    "ApiKey": "********************************"
  },
  "Discord": {
    "BotToken": "MTM4NTA1OTI3MDMzNjMxNTQ1NA.GlZAu0.dZaZAZdW5ivUiXDBDE6yqoPI-HdGA9uh2kX8qo",
    "ChannelId": "1385057459814797383"
  },
  "Gemini": {
    "ApiKey": "AIzaSyCh_cCKoG5cY7WeSIel65-G7HbzDQQy1qc"
  },
  "AlpacaNews": {
    "KeyId": "PK0AM3WB1CES3YBQPGR0",
    "Secret": "2O4bJsHpGjyYU6FvQ956kIJaLePdBihZveKutbtf"
  },
  "Safety": {
    "AllowedEnvironment": "Live",
    "MaxDailyLoss": 500,
    "MaxPositions": 8,
    "MaxSingleTradeValue": 3000,
    "MinAccountEquity": 5000,
    "MaxPositionSizePercent": 0.12,
    "MaxDailyTrades": 25,
    "RequireConfirmation": false,
    "DryRunMode": false
  },
  "Strategy": {
    "UniverseSize": 5000,
    "TopNSymbols": 10,
    "VixThreshold": 25.0,
    "EnableRegimeFilter": true,
    "EnableVolatilityFilter": true
  },
  "USE_DYNAMIC_UNIVERSE": true,

  // Enable Polygon-based universe management
  "UsePolygonUniverse": true,

  // Polygon Symbol Universe Configuration
  "PolygonUniverse": {
    "PageSize": 1000,
    "MaxSymbols": 0,
    "IncludedMarkets": ["stocks"],
    "IncludedTypes": ["CS", "ETF", "ADRC"],
    "ActiveOnly": true,
    "DelayBetweenCalls": 200,
    "CacheTtlHours": 168
  },

  // Polygon Snapshot Service Configuration
  "PolygonSnapshot": {
    "BatchSize": 50,
    "MaxConcurrency": 5,
    "DelayBetweenCalls": 200
  },

  // Daily Universe Refresh Configuration
  "UniverseRefresh": {
    "RefreshTimeUtc": "12:30:00",
    "MinPrice": 2.0,
    "MinAverageVolume": 50000,
    "MinVolatilityPercent": 0.3,
    "MaxCandidates": 10000,
    "AnalysisPeriodDays": 60,
    "MinMarketCap": null,
    "CacheTtlHours": 24,
    "ExcludedExchanges": [],
    "ExcludedTypes": []
  },

  // Enhanced DynamicUniverseProvider Configuration
  "DynamicUniverse": {
    "UsePolygonIntegration": true,
    "FallbackToStaticSymbols": true,
    "MaxConcurrentBatches": 10,
    "BatchSize": 50,
    "DefaultCriteria": {
      "MinPrice": 2.0,
      "MinAverageVolume": 50000,
      "MinVolatilityPercent": 0.3,
      "AnalysisPeriodDays": 60,
      "MaxSymbols": 5000
    }
  },

  "Options": {
    "EnableOptionsOverlay": false,
    "EnableProtectivePuts": false,
    "EnableCoveredCalls": false
  },
  "VWAPMonitor": {
    "RollingMinutes": 30,
    "MinTradesRequired": 2,
    "MinVolumeThreshold": 100,
    "RequireTrendingRegime": false,
    "DeviationAlertThreshold": 2.0,
    "CacheExpiryMinutes": 5,
    "EnableVWAPFilter": true
  },
  "ML": {
    "PositionModelPath": "SmaTrendFollower.Console/Model/position_model.zip"
  },
  "SlippageTraining": {
    "ModelOutputPath": "SmaTrendFollower.Console/Model/slippage_model.zip"
  },
  "AccountStreaming": {
    "RefreshIntervalSeconds": 15
  },
  "DataStaleness": {
    "MarketHours": {
      "HistoricalBars": "00:18:00",
      "RealTimeQuotes": "00:02:00",
      "IndexData": "00:15:00",
      "VixData": "00:15:00",
      "UniverseData": "04:00:00",
      "SignalData": "00:30:00",
      "IndicatorData": "00:20:00"
    },
    "AfterHours": {
      "HistoricalBars": "08:00:00",
      "RealTimeQuotes": "08:00:00",
      "IndexData": "08:00:00",
      "VixData": "08:00:00",
      "UniverseData": "24:00:00",
      "SignalData": "08:00:00",
      "IndicatorData": "08:00:00"
    },
    "EnableStrictstalenessChecks": true,
    "RejectStaleData": true,
    "LogStalenessWarnings": true
  },
  "PerformanceMonitoring": {
    "SystemResources": {
      "MonitoringIntervalSeconds": 5,
      "CpuWarningThreshold": 80.0,
      "CpuCriticalThreshold": 95.0,
      "MemoryWarningThreshold": 85.0,
      "MemoryCriticalThreshold": 95.0,
      "ThreadCountWarning": 100,
      "ThreadCountCritical": 200
    },
    "Database": {
      "EnableSqliteMonitoring": true,
      "EnableRedisMonitoring": true,
      "QueryLatencyWarningMs": 1000,
      "QueryLatencyCriticalMs": 5000,
      "RedisLatencyWarningMs": 100,
      "RedisLatencyCriticalMs": 500
    },
    "WebSocket": {
      "EnableConnectionMonitoring": true,
      "EnableThroughputMonitoring": true,
      "MinConnectionRatio": 0.8,
      "MaxReconnectionsPerHour": 10,
      "MessageLatencyWarningMs": 100,
      "MessageLatencyCriticalMs": 1000
    },
    "TradingPipeline": {
      "EnablePipelineMonitoring": true,
      "EnableBottleneckDetection": true,
      "MaxActiveExecutions": 50,
      "MaxBottlenecksPer5Min": 10,
      "SignalLatencyWarningMs": 5000,
      "SignalLatencyCriticalMs": 15000,
      "TradeExecutionLatencyWarningMs": 2000,
      "TradeExecutionLatencyCriticalMs": 10000
    },
    "Alerting": {
      "EnableDiscordNotifications": true,
      "EnableEmailNotifications": false,
      "CriticalAlertCooldownMinutes": 5,
      "WarningAlertCooldownMinutes": 15,
      "HealthScoreWarning": 70.0,
      "HealthScoreCritical": 50.0
    },
    "LoadTesting": {
      "EnableAutomaticTesting": false,
      "TestSchedule": "0 2 * * 0",
      "DefaultTestDurationMinutes": 5,
      "MaxConcurrentOperations": 50,
      "EnableStressTesting": true
    }
  }
}
