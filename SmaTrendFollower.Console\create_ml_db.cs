using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Data;

namespace SmaTrendFollower.Console;

/// <summary>
/// Simple utility to create the ML features database
/// </summary>
public static class CreateMLDatabase
{
    public static async Task CreateAsync()
    {
        System.Console.WriteLine("Creating ML Features database...");

        var baseConnectionString = "Host=*************;Port=5432;Database=tradingbot_db;Username=tradingbot_user;Password=your_strong_postgres_password;";

        // Apply optimized connection string with increased pool size and performance settings
        var optimizedConnectionString = new Npgsql.NpgsqlConnectionStringBuilder(baseConnectionString)
        {
            MaxPoolSize = 200, // Increased from default 100 to handle high concurrency
            MinPoolSize = 20,  // Increased from default 1 to maintain warm connections
            Pooling = true,
            ConnectionIdleLifetime = 300, // 5 minutes
            CommandTimeout = 30,
            Timeout = 30,
            Multiplexing = true,
            ReadBufferSize = 8192,
            WriteBufferSize = 8192
        }.ConnectionString;

        var options = new DbContextOptionsBuilder<MLFeaturesDbContext>()
            .UseNpgsql(optimizedConnectionString)
            .Options;

        using var context = new MLFeaturesDbContext(options);

        // Create the database and tables
        await context.Database.EnsureCreatedAsync();

        System.Console.WriteLine("✅ ML Features database created successfully!");
        System.Console.WriteLine("Database: tradingbot_db (PostgreSQL)");
        System.Console.WriteLine("Tables created: Features, FillsLog");
    }
}
