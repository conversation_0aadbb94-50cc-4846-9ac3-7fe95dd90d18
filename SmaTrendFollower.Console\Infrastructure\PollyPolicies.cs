using Microsoft.Extensions.Logging;
using <PERSON>;
using Polly.Extensions.Http;
using System.Net;

namespace SmaTrendFollower.Infrastructure;

/// <summary>
/// Centralized Polly resilience policies for HTTP clients
/// Provides robust retry and circuit-breaker patterns for external API calls
/// </summary>
public static class PollyPolicies
{
    /// <summary>
    /// Retry policy with exponential back-off for transient failures
    /// - 5 attempts with exponential back-off (1s, 2s, 4s, 8s, 16s)
    /// - Handles 5xx errors, network failures, timeouts, and 429 rate limiting
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> RetryPolicy =>
        HttpPolicyExtensions
            .HandleTransientHttpError()               // 5xx + network failures
            .Or<TaskCanceledException>()              // Timeout exceptions
            .Or<TimeoutException>()                   // Explicit timeout exceptions
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429) // Rate limiting
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503) // Service unavailable
            .WaitAndRetryAsync(5, retryAttempt =>
                TimeSpan.FromSeconds(Math.Pow(2, retryAttempt - 1))); // 1s, 2s, 4s, 8s, 16s

    /// <summary>
    /// Circuit breaker policy to prevent cascading failures
    /// - Opens after 8 consecutive failures (increased tolerance)
    /// - Stays open for 60 seconds before allowing test requests
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> CircuitBreakerPolicy =>
        HttpPolicyExtensions
            .HandleTransientHttpError()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503)
            .CircuitBreakerAsync(8, TimeSpan.FromSeconds(60));

    /// <summary>
    /// Enhanced retry policy with comprehensive logging
    /// - 5 attempts with exponential back-off and jitter
    /// - Handles timeouts, network failures, and rate limiting
    /// - Detailed logging of retry attempts with URL and failure reasons
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetRetryPolicyWithLogging(ILogger logger, string apiName)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503)
            .WaitAndRetryAsync(
                retryCount: 5,
                sleepDurationProvider: retryAttempt =>
                    TimeSpan.FromSeconds(Math.Pow(2, retryAttempt - 1)) +
                    TimeSpan.FromMilliseconds(Random.Shared.Next(0, 1000)), // Add jitter
                onRetry: (outcome, timespan, retry, ctx) =>
                {
                    var url = outcome.Result?.RequestMessage?.RequestUri?.ToString() ?? "Unknown URL";
                    var reason = outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString() ?? "Unknown";

                    logger.LogWarning("Retry #{Retry} for {ApiName} {Url} due to {Reason}. Waiting {Delay}ms",
                        retry, apiName, url, reason, timespan.TotalMilliseconds);
                });
    }

    /// <summary>
    /// Enhanced circuit breaker policy with comprehensive logging
    /// - Opens after 8 consecutive failures for 60 seconds (increased tolerance)
    /// - Handles timeouts and network failures
    /// - Logs circuit breaker state changes for monitoring
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetCircuitBreakerPolicyWithLogging(ILogger logger, string apiName)
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .Or<TaskCanceledException>()
            .Or<TimeoutException>()
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)429)
            .OrResult(msg => msg.StatusCode == (HttpStatusCode)503)
            .CircuitBreakerAsync(
                handledEventsAllowedBeforeBreaking: 8,
                durationOfBreak: TimeSpan.FromSeconds(60),
                onBreak: (result, duration) =>
                {
                    var reason = result.Exception?.Message ?? result.Result?.StatusCode.ToString() ?? "Unknown";
                    logger.LogError("{ApiName} circuit breaker OPENED for {Duration}s. Last error: {Reason}",
                        apiName, duration.TotalSeconds, reason);
                },
                onReset: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker RESET - service recovered", apiName);
                },
                onHalfOpen: () =>
                {
                    logger.LogInformation("{ApiName} circuit breaker HALF-OPEN - testing service", apiName);
                });
    }

    /// <summary>
    /// Timeout policy to prevent hanging requests
    /// - 25 second timeout (slightly less than HttpClient default)
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> TimeoutPolicy =>
        Policy.TimeoutAsync<HttpResponseMessage>(TimeSpan.FromSeconds(25));

    /// <summary>
    /// Combined policy wrapper for complete resilience
    /// - Combines timeout, retry, and circuit breaker policies
    /// - Applies policies in the correct order for optimal behavior
    /// </summary>
    public static IAsyncPolicy<HttpResponseMessage> GetCombinedPolicy(ILogger logger, string apiName)
    {
        var timeout = TimeoutPolicy;
        var retry = GetRetryPolicyWithLogging(logger, apiName);
        var circuitBreaker = GetCircuitBreakerPolicyWithLogging(logger, apiName);

        // Apply policies in order: Timeout -> Retry -> Circuit Breaker
        return Policy.WrapAsync(timeout, retry, circuitBreaker);
    }
}
