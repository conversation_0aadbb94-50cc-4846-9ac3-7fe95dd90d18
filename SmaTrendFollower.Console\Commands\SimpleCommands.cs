using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using SmaTrendFollower.Services;
using SmaTrendFollower.Data;
using SmaTrendFollower.Configuration;
using SmaTrendFollower.Models;
using DotNetEnv;
using Alpaca.Markets;

namespace SmaTrendFollower.Commands;

/// <summary>
/// Simple command handlers for Phase 3 & 4 functionality demonstration
/// </summary>
public static class SimpleCommands
{
    /// <summary>
    /// Handles health status command
    /// </summary>
    public static async Task HandleHealthAsync(IServiceProvider services)
    {
        try
        {
            var healthService = services.GetRequiredService<ISystemHealthService>();
            var status = healthService.GetCurrentStatus();
            var report = await healthService.GetHealthReportAsync();

            System.Console.WriteLine($"🏥 System Health Status: {status}");
            System.Console.WriteLine($"📅 Generated: {report.GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC");
            System.Console.WriteLine();

            System.Console.WriteLine("📊 Component Health:");
            foreach (var check in report.Checks.Values)
            {
                var icon = check.Status switch
                {
                    HealthCheckStatus.Healthy => "✅",
                    HealthCheckStatus.Degraded => "⚠️",
                    HealthCheckStatus.Unhealthy => "❌",
                    _ => "❓"
                };

                System.Console.WriteLine($"  {icon} {check.Name}: {check.Status}");
                if (!string.IsNullOrEmpty(check.Details))
                {
                    System.Console.WriteLine($"     {check.Details}");
                }
            }

            if (report.RecentEvents.Any())
            {
                System.Console.WriteLine();
                System.Console.WriteLine("📋 Recent Events:");
                foreach (var evt in report.RecentEvents.TakeLast(3))
                {
                    var severityIcon = evt.Severity switch
                    {
                        SmaTrendFollower.Services.HealthEventSeverity.Critical => "🔴",
                        SmaTrendFollower.Services.HealthEventSeverity.High => "🟠",
                        SmaTrendFollower.Services.HealthEventSeverity.Medium => "🟡",
                        SmaTrendFollower.Services.HealthEventSeverity.Low => "🟢",
                        _ => "⚪"
                    };

                    System.Console.WriteLine($"  {severityIcon} [{evt.Component}] {evt.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error checking health: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles metrics display command
    /// </summary>
    public static async Task HandleMetricsAsync(IServiceProvider services)
    {
        try
        {
            var metricsService = services.GetRequiredService<ITradingMetricsService>();
            var stats = await metricsService.GetTradingStatisticsAsync();
            var kpis = metricsService.GetKPIs();

            System.Console.WriteLine("📈 Trading Statistics:");
            System.Console.WriteLine($"  📊 Total Trades: {stats.TotalTrades}");
            System.Console.WriteLine($"  💰 Profitable Trades: {stats.ProfitableTrades}");
            System.Console.WriteLine($"  🎯 Win Rate: {stats.WinRate:P2}");
            System.Console.WriteLine($"  💵 Total P&L: ${stats.TotalPnL:F2}");
            System.Console.WriteLine($"  📊 Sharpe Ratio: {stats.SharpeRatio:F2}");
            System.Console.WriteLine($"  📡 Total Signals: {stats.TotalSignals}");
            System.Console.WriteLine($"  ✅ Executed Signals: {stats.ExecutedSignals}");
            System.Console.WriteLine($"  🎯 Signal Execution Rate: {stats.SignalExecutionRate:P2}");
            System.Console.WriteLine();

            System.Console.WriteLine("🔑 Key Performance Indicators:");
            foreach (var kpi in kpis.Take(5))
            {
                System.Console.WriteLine($"  📊 {kpi.Key}: {kpi.Value:F2}");
            }

            System.Console.WriteLine($"📅 Generated: {stats.GeneratedAt:yyyy-MM-dd HH:mm:ss} UTC");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error retrieving metrics: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles live market monitoring command
    /// </summary>
    public static Task HandleLiveAsync(IServiceProvider services)
    {
        try
        {
            var marketMonitor = services.GetRequiredService<IRealTimeMarketMonitor>();
            var signalIntelligence = services.GetRequiredService<ILiveSignalIntelligence>();

            var snapshots = marketMonitor.GetAllMarketSnapshots();
            var signals = signalIntelligence.GetLiveSignals();
            var alerts = marketMonitor.GetRecentAlerts(5);

            System.Console.WriteLine($"📡 Live Market Data ({snapshots.Count} symbols monitored):");
            foreach (var snapshot in snapshots.Values.Take(5))
            {
                var trendIcon = snapshot.Trend switch
                {
                    MarketTrend.Bullish => "📈",
                    MarketTrend.Bearish => "📉",
                    MarketTrend.Sideways => "➡️",
                    _ => "❓"
                };

                System.Console.WriteLine($"  {trendIcon} {snapshot.Symbol}: ${snapshot.CurrentPrice:F2} " +
                                        $"({snapshot.PriceChange:P2}) Vol: {snapshot.Volatility:F4}");
            }

            System.Console.WriteLine();
            System.Console.WriteLine($"🧠 Live Signals ({signals.Count} active):");
            foreach (var signal in signals.Take(3))
            {
                System.Console.WriteLine($"  📊 {signal.Symbol}: ${signal.Price:F2} " +
                                        $"Confidence: {signal.Confidence:F2} Score: {signal.IntelligenceScore:F2}");
                System.Console.WriteLine($"     💡 {signal.Reasoning}");
            }

            if (alerts.Any())
            {
                System.Console.WriteLine();
                System.Console.WriteLine($"🚨 Recent Alerts ({alerts.Count}):");
                foreach (var alert in alerts.Take(3))
                {
                    var severityIcon = alert.Severity switch
                    {
                        AlertSeverity.Critical => "🔴",
                        AlertSeverity.High => "🟠",
                        AlertSeverity.Medium => "🟡",
                        AlertSeverity.Low => "🟢",
                        _ => "⚪"
                    };

                    System.Console.WriteLine($"  {severityIcon} [{alert.Symbol}] {alert.Description}");
                }
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error retrieving live data: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Handles system status command
    /// </summary>
    public static Task HandleSystemAsync(IServiceProvider services)
    {
        try
        {
            var version = typeof(Program).Assembly.GetName().Version?.ToString() ?? "Unknown";
            var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Production";
            var process = System.Diagnostics.Process.GetCurrentProcess();
            var startTime = process.StartTime;
            var uptime = DateTime.Now - startTime;
            var memoryMB = process.WorkingSet64 / 1024 / 1024;

            System.Console.WriteLine("🖥️  System Status:");
            System.Console.WriteLine($"  📦 Version: {version}");
            System.Console.WriteLine($"  🌍 Environment: {environment}");
            System.Console.WriteLine($"  🕐 Started: {startTime:yyyy-MM-dd HH:mm:ss}");
            System.Console.WriteLine($"  ⏱️  Uptime: {uptime.Days}d {uptime.Hours}h {uptime.Minutes}m");
            System.Console.WriteLine($"  🆔 Process ID: {Environment.ProcessId}");
            System.Console.WriteLine($"  💻 Machine: {Environment.MachineName}");
            System.Console.WriteLine($"  🖥️  OS: {Environment.OSVersion}");
            System.Console.WriteLine($"  ⚙️  .NET: {Environment.Version}");
            System.Console.WriteLine($"  🧠 Memory: {memoryMB}MB");
            System.Console.WriteLine($"  🧵 Threads: {process.Threads.Count}");

            // Try to get additional metrics
            try
            {
                var metricsService = services.GetRequiredService<ITradingMetricsService>();
                var systemMetrics = metricsService.GetRecentSystemMetrics(5);
                
                if (systemMetrics.Any())
                {
                    System.Console.WriteLine();
                    System.Console.WriteLine("📊 Recent System Metrics:");
                    foreach (var metric in systemMetrics.TakeLast(3))
                    {
                        System.Console.WriteLine($"  📈 {metric.MetricName}: {metric.Value:F2} {metric.Unit}");
                    }
                }
            }
            catch
            {
                // Metrics service might not be available
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error retrieving system status: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    /// <summary>
    /// Tests VIX fallback strategy
    /// </summary>
    public static async Task HandleVixFallbackTestAsync(IServiceProvider services)
    {
        try
        {
            System.Console.WriteLine("🔍 Testing VIX Fallback Strategy...");
            System.Console.WriteLine();

            var marketDataService = services.GetRequiredService<IMarketDataService>();
            var vixFallbackService = services.GetRequiredService<IVixFallbackService>();

            // Test 1: Full VIX analysis with fallback
            System.Console.WriteLine("📊 Test 1: VIX Analysis with Fallback Strategy");
            var vixAnalysis = await marketDataService.GetVixAnalysisAsync();
            System.Console.WriteLine($"  ✅ Current VIX: {vixAnalysis.CurrentVix:F2}");
            System.Console.WriteLine($"  📈 VIX SMA30: {vixAnalysis.VixSma30:F2}");
            System.Console.WriteLine($"  🔺 Above SMA: {vixAnalysis.IsAboveSma}");
            System.Console.WriteLine($"  ⚡ Is Spike: {vixAnalysis.IsSpike}");
            System.Console.WriteLine($"  🕐 Timestamp: {vixAnalysis.Timestamp:yyyy-MM-dd HH:mm:ss} UTC");
            System.Console.WriteLine();

            // Test 2: VIX spike detection
            System.Console.WriteLine("🚨 Test 2: VIX Spike Detection");
            var isSpike25 = await marketDataService.IsVixSpikeAsync(25.0m);
            var isSpike30 = await marketDataService.IsVixSpikeAsync(30.0m);
            System.Console.WriteLine($"  🔥 Spike > 25: {isSpike25}");
            System.Console.WriteLine($"  🔥 Spike > 30: {isSpike30}");
            System.Console.WriteLine();

            // Test 3: Web fallback
            System.Console.WriteLine("🌐 Test 3: Web Fallback");
            var webVix = await vixFallbackService.GetVixFromWebAsync();
            if (webVix.HasValue)
            {
                System.Console.WriteLine($"  ✅ Web VIX: {webVix.Value:F2}");
            }
            else
            {
                System.Console.WriteLine($"  ⚠️ Web VIX: Not available");
            }
            System.Console.WriteLine();

            // Test 4: Synthetic VIX calculation
            System.Console.WriteLine("🧮 Test 4: Synthetic VIX Calculation");
            var syntheticVix = await vixFallbackService.CalculateSyntheticVixAsync();
            if (syntheticVix.HasValue)
            {
                System.Console.WriteLine($"  ✅ Synthetic VIX: {syntheticVix.Value:F2}");
            }
            else
            {
                System.Console.WriteLine($"  ⚠️ Synthetic VIX: Not available");
            }
            System.Console.WriteLine();

            System.Console.WriteLine("✅ VIX Fallback Strategy Test Completed");
            System.Console.WriteLine();
            System.Console.WriteLine("📝 Data Source Priority:");
            System.Console.WriteLine("  1. 🥇 Polygon API (I:VIX) - Primary source");
            System.Console.WriteLine("  2. 🥈 Web scraping (Yahoo, MarketWatch) - Secondary fallback");
            System.Console.WriteLine("  3. 🥉 Synthetic VIX (VXX, UVXY ETFs) - Tertiary fallback");
            System.Console.WriteLine("  4. 🛡️ Default value (20.0) - Final conservative fallback");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error testing VIX fallback: {ex.Message}");
            if (ex.InnerException != null)
            {
                System.Console.WriteLine($"   Inner: {ex.InnerException.Message}");
            }
        }
    }

    /// <summary>
    /// Shows available commands
    /// </summary>
    public static void ShowHelp()
    {
        System.Console.WriteLine("🚀 SmaTrendFollower Enhanced Commands (Phases 3 & 4)");
        System.Console.WriteLine();
        System.Console.WriteLine("📋 Available Commands:");
        System.Console.WriteLine("  health         - 🏥 Show system health status");
        System.Console.WriteLine("  metrics        - 📈 Show trading metrics and statistics");
        System.Console.WriteLine("  live           - 📡 Show live market data and signals");
        System.Console.WriteLine("  system         - 🖥️  Show system status and information");
        System.Console.WriteLine("  test-vix       - 🔍 Test VIX fallback strategy");
        System.Console.WriteLine("  test-parallel  - 🚀 Test parallel signal generation performance");
        System.Console.WriteLine("  test-paper-trade - 🧪 Test paper trading execution");
        System.Console.WriteLine("  test-state     - 🔄 Test state restoration system");
        System.Console.WriteLine("  metrics-api    - 🌐 Start web dashboard API service");
        System.Console.WriteLine();
        System.Console.WriteLine("🤖 Machine Learning Commands:");
        System.Console.WriteLine("  ml-export      - 📊 Export ML training features to CSV");
        System.Console.WriteLine("  ml-train       - 🎯 Train new ML signal ranking model");
        System.Console.WriteLine("  ml-validate    - ✅ Validate ML training features");
        System.Console.WriteLine("  ml-info        - ℹ️  Show current ML model information");
        System.Console.WriteLine("  ml-retrain     - 🔄 Trigger manual ML model retraining");
        System.Console.WriteLine();
        System.Console.WriteLine("🎯 Position Sizing Commands:");
        System.Console.WriteLine("  position-export   - 📊 Export position sizing features to CSV");
        System.Console.WriteLine("  position-train    - 🤖 Train new position sizing model");
        System.Console.WriteLine("  position-retrain  - 🔄 Trigger manual position sizer retraining");
        System.Console.WriteLine("  position-info     - ℹ️  Show position sizer model information");
        System.Console.WriteLine();
        System.Console.WriteLine("🔄 Trading Modes:");
        System.Console.WriteLine("  (default)         - 🔄 Continuous trading mode (runs until stopped)");
        System.Console.WriteLine("  --single-cycle    - 🔄 Run single trading cycle and exit");
        System.Console.WriteLine();
        System.Console.WriteLine("🛡️ Safety Commands:");
        System.Console.WriteLine("  --show-safety     - Show current safety configuration");
        System.Console.WriteLine("  --check-account   - Check account status and connectivity");
        System.Console.WriteLine("  --dry-run         - Run in simulation mode (no real trades)");
        System.Console.WriteLine("  --confirm         - Confirm live trading (required for real money)");
        System.Console.WriteLine();
        System.Console.WriteLine("💡 Usage Examples:");
        System.Console.WriteLine("  dotnet run                    - 🔄 Run continuous trading (recommended)");
        System.Console.WriteLine("  dotnet run -- --confirm       - 🔄 Run continuous live trading");
        System.Console.WriteLine("  dotnet run -- --single-cycle  - Run single cycle and exit");
        System.Console.WriteLine("  dotnet run -- health          - Check system health");
        System.Console.WriteLine("  dotnet run -- metrics         - Show trading metrics");
        System.Console.WriteLine("  dotnet run -- live            - Show live market data");
        System.Console.WriteLine("  dotnet run -- system          - Show system status");
        System.Console.WriteLine("  dotnet run -- metrics-api     - Start web dashboard");
        System.Console.WriteLine();
        System.Console.WriteLine("🌐 Web Dashboard:");
        System.Console.WriteLine("  After starting metrics-api, visit:");
        System.Console.WriteLine("  http://localhost:8080/dashboard");
        System.Console.WriteLine();
        System.Console.WriteLine("💡 Continuous Mode:");
        System.Console.WriteLine("  - Runs trading cycles every 30 minutes");
        System.Console.WriteLine("  - Automatically pauses when markets are closed");
        System.Console.WriteLine("  - Press Ctrl+C to stop gracefully");
        System.Console.WriteLine("  - Resumes trading when markets reopen");
        System.Console.WriteLine();
        System.Console.WriteLine("🎯 Phase 3: Live Intelligence & Streaming - ✅ IMPLEMENTED");
        System.Console.WriteLine("🔍 Phase 4: Observability & Deployment - ✅ IMPLEMENTED");
    }

    /// <summary>
    /// Runs a command with proper service setup
    /// </summary>
    public static async Task RunCommandAsync(string command, Func<IServiceProvider, Task> handler)
    {
        try
        {
            var host = Microsoft.Extensions.Hosting.Host.CreateDefaultBuilder()
                .ConfigureServices((context, services) =>
                {
                    // Configure all services needed for the command
                    ConfigureCommandServices(services);
                })
                .Build();

            using var scope = host.Services.CreateScope();
            await handler(scope.ServiceProvider);
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error executing {command} command: {ex.Message}");
        }
    }

    /// <summary>
    /// Handles manual regime classification command
    /// </summary>
    public static async Task HandleClassifyTodayAsync(IServiceProvider services)
    {
        try
        {
            var regimeClassifier = services.GetRequiredService<IRegimeClassifierService>();
            var logger = services.GetRequiredService<ILogger<IRegimeClassifierService>>();

            System.Console.WriteLine("🔍 Running manual regime classification...");
            System.Console.WriteLine();

            var regime = await regimeClassifier.DetectTodayAsync();
            var modelVersion = await regimeClassifier.GetModelVersionAsync();
            var cachedRegime = await regimeClassifier.GetCachedRegimeAsync();

            System.Console.WriteLine("📊 Regime Classification Results:");
            System.Console.WriteLine($"  🎯 Current Regime: {regime}");
            System.Console.WriteLine($"  📦 Model Version: {modelVersion}");
            System.Console.WriteLine($"  💾 Cached Regime: {cachedRegime?.ToString() ?? "None"}");
            System.Console.WriteLine($"  🕐 Timestamp: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            System.Console.WriteLine();

            // Display regime interpretation
            var interpretation = regime switch
            {
                MarketRegime.Sideways => "📈 Neutral/Sideways market conditions",
                MarketRegime.TrendingUp => "🚀 Trending up market conditions",
                MarketRegime.TrendingDown => "📉 Trending down market conditions",
                MarketRegime.Panic => "⚠️ Panic/High volatility market conditions",
                _ => "❓ Unknown regime"
            };

            System.Console.WriteLine($"💡 Interpretation: {interpretation}");
            System.Console.WriteLine();
            System.Console.WriteLine("✅ Manual regime classification completed successfully!");
        }
        catch (Exception ex)
        {
            var logger = services.GetRequiredService<ILogger<IRegimeClassifierService>>();
            logger.LogError(ex, "Error during manual regime classification");

            System.Console.WriteLine("❌ Error during manual regime classification:");
            System.Console.WriteLine($"   {ex.Message}");

            if (ex.InnerException != null)
            {
                System.Console.WriteLine($"   Inner: {ex.InnerException.Message}");
            }
        }
    }

    /// <summary>
    /// Configures lightweight services for command execution (no heavy dependencies)
    /// </summary>
    private static void ConfigureCommandServices(IServiceCollection services)
    {
        // Load environment variables
        DotNetEnv.Env.Load();

        // Determine environment
        var environment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
        var configEnvironment = environment == "Development" ? "LocalProd" : environment;

        // Build configuration with proper appsettings files
        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("SmaTrendFollower.Console/appsettings.json", optional: true)
            .AddJsonFile($"SmaTrendFollower.Console/appsettings.{configEnvironment}.json", optional: true)
            .AddJsonFile("appsettings.json", optional: true)
            .AddJsonFile($"appsettings.{configEnvironment}.json", optional: true)
            .AddEnvironmentVariables()
            .Build();

        // Add only essential services for commands (no heavy dependencies)
        services.AddSingleton<IConfiguration>(configuration);
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.SetMinimumLevel(LogLevel.Information);
        });

        // Add lightweight mock services for commands that don't need full system
        services.AddSingleton<ISystemHealthService, MockSystemHealthService>();
        services.AddSingleton<ITradingMetricsService, MockTradingMetricsService>();

        // Add state restoration services for test-state command
        // Use mock services for testing since we don't want full setup in commands
        services.AddSingleton<ILiveStateStore, MockLiveStateStore>();
        services.AddSingleton<IBarStore, MockBarStore>();
        services.AddSingleton<StateFlushService>();

        // Note: Universe refresh now uses dedicated lightweight services
    }

    /// <summary>
    /// Tests the bar recorder functionality
    /// </summary>
    public static async Task HandleTestBarRecorderAsync(IServiceProvider services)
    {
        try
        {
            var logger = services.GetRequiredService<ILogger<BarRecorder>>();
            var barRecorder = services.GetRequiredService<IBarRecorder>();

            System.Console.WriteLine("🔄 Testing Bar Recorder functionality...");

            // Create test bars for today
            var today = DateTime.UtcNow.Date;
            var testBars = new List<TestBar>
            {
                new TestBar("AAPL", today.AddHours(9).AddMinutes(30), 150.00m, 152.00m, 149.00m, 151.00m, 1000000),
                new TestBar("AAPL", today.AddHours(9).AddMinutes(31), 151.00m, 153.00m, 150.50m, 152.50m, 1200000),
                new TestBar("AAPL", today.AddHours(9).AddMinutes(32), 152.50m, 154.00m, 152.00m, 153.75m, 900000),
                new TestBar("AAPL", today.AddHours(9).AddMinutes(33), 153.75m, 155.00m, 153.00m, 154.25m, 1100000),
                new TestBar("AAPL", today.AddHours(9).AddMinutes(34), 154.25m, 156.00m, 154.00m, 155.50m, 950000)
            };

            System.Console.WriteLine($"📊 Recording {testBars.Count} test bars for AAPL...");
            barRecorder.Record(testBars, "AAPL", "Minute");

            // Test with daily bars too
            var dailyBars = new List<TestBar>
            {
                new TestBar("SPY", today, 420.00m, 425.00m, 418.00m, 423.50m, 50000000),
                new TestBar("QQQ", today, 350.00m, 355.00m, 348.00m, 352.75m, 30000000)
            };

            System.Console.WriteLine($"📊 Recording {dailyBars.Count} daily test bars...");
            barRecorder.Record(dailyBars.Take(1), "SPY", "Day");
            barRecorder.Record(dailyBars.Skip(1), "QQQ", "Day");

            // Wait for compression to complete
            System.Console.WriteLine("⏳ Waiting for compression to complete...");
            await Task.Delay(3000);

            var dataPath = Path.Combine("Data", "Bars", today.ToString("yyyy"), today.ToString("MM"), today.ToString("dd"));
            System.Console.WriteLine($"✅ Bar recording test completed!");
            System.Console.WriteLine($"📁 Check directory: {Path.GetFullPath(dataPath)}");

            if (Directory.Exists(dataPath))
            {
                var files = Directory.GetFiles(dataPath, "*.zst");
                System.Console.WriteLine($"📄 Created {files.Length} compressed files:");
                foreach (var file in files)
                {
                    var fileInfo = new FileInfo(file);
                    System.Console.WriteLine($"   - {Path.GetFileName(file)} ({fileInfo.Length} bytes)");
                }
            }
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error testing bar recorder: {ex.Message}");
            System.Console.WriteLine($"🔍 Stack trace: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// Tests the MarketDataService bar recording integration
    /// </summary>
    public static async Task HandleTestMarketDataBarRecordingAsync(IServiceProvider services)
    {
        await SmaTrendFollower.Tests.MarketDataBarRecordingTest.TestMarketDataServiceIntegration();
    }

    /// <summary>
    /// Tests paper trading by executing a simple trade
    /// </summary>
    public static async Task HandleTestPaperTradeAsync(IServiceProvider services)
    {
        try
        {
            System.Console.WriteLine("🧪 Testing Paper Trading...");
            System.Console.WriteLine();

            // Get the Alpaca client directly to test account access
            var alpacaFactory = services.GetRequiredService<IAlpacaClientFactory>();
            var tradingClient = alpacaFactory.CreateTradingClient();

            System.Console.WriteLine("📊 Testing Alpaca account access...");
            var account = await tradingClient.GetAccountAsync();
            System.Console.WriteLine($"✅ Account connected: {account.AccountId}");
            System.Console.WriteLine($"💰 Buying Power: ${account.BuyingPower:F2}");
            System.Console.WriteLine($"📈 Portfolio Value: ${account.Equity:F2}");
            System.Console.WriteLine($"🏦 Account Status: {account.Status}");
            System.Console.WriteLine();

            // Test market session
            var marketSessionGuard = services.GetRequiredService<IMarketSessionGuard>();
            var canTrade = await marketSessionGuard.CanTradeNowAsync();
            System.Console.WriteLine($"📊 Market Session Check: {(canTrade ? "✅ Can Trade" : "❌ Cannot Trade")}");

            if (!canTrade)
            {
                System.Console.WriteLine($"⚠️ Reason: {marketSessionGuard.Reason}");
                System.Console.WriteLine("⚠️ Note: Paper trading test will proceed anyway for testing purposes");
            }

            // Test portfolio gate
            var portfolioGate = services.GetRequiredService<IPortfolioGate>();
            var portfolioAllowed = await portfolioGate.ShouldTradeAsync();
            System.Console.WriteLine($"🚪 Portfolio Gate: {(portfolioAllowed ? "✅ Open" : "❌ Closed")}");
            System.Console.WriteLine();

            // Create a test signal manually
            System.Console.WriteLine("🎯 Creating test signal for AAPL...");
            var testSignal = new TradingSignal("AAPL", 150.00m, 2.50m, 0.15m, 0.75m);
            System.Console.WriteLine($"🎯 Test Signal: {testSignal.Symbol} @ ${testSignal.Price:F2}");

            // Test risk manager
            var riskManager = services.GetRequiredService<IRiskManager>();
            var quantity = await riskManager.CalculateQuantityAsync(testSignal);
            System.Console.WriteLine($"📊 Risk Manager Calculated Quantity: {quantity} shares");

            // Force a small quantity for testing if risk manager returns 0
            if (quantity <= 0)
            {
                quantity = 1; // Force 1 share for testing
                System.Console.WriteLine($"⚠️ Risk manager returned 0, forcing 1 share for test");
            }

            System.Console.WriteLine();
            System.Console.WriteLine("🚀 Executing paper trade...");

            // Execute the trade directly via Alpaca
            var orderRequest = new NewOrderRequest(testSignal.Symbol, (int)Math.Ceiling(quantity), OrderSide.Buy, OrderType.Market, TimeInForce.Day);

            System.Console.WriteLine($"📤 Submitting order: BUY {quantity} shares of {testSignal.Symbol}");
            var order = await tradingClient.PostOrderAsync(orderRequest);

            System.Console.WriteLine($"✅ Paper trade order submitted successfully!");
            System.Console.WriteLine($"   Order ID: {order.OrderId}");
            System.Console.WriteLine($"   Symbol: {order.Symbol}");
            System.Console.WriteLine($"   Quantity: {order.Quantity} shares");
            System.Console.WriteLine($"   Side: {order.OrderSide}");
            System.Console.WriteLine($"   Type: {order.OrderType}");
            System.Console.WriteLine($"   Status: {order.OrderStatus}");
            System.Console.WriteLine($"   Submitted At: {order.SubmittedAtUtc:yyyy-MM-dd HH:mm:ss} UTC");

            // Wait a moment and check order status
            System.Console.WriteLine();
            System.Console.WriteLine("⏳ Waiting 3 seconds to check order status...");
            await Task.Delay(3000);

            var updatedOrder = await tradingClient.GetOrderAsync(order.OrderId);
            System.Console.WriteLine($"📊 Updated Order Status: {updatedOrder.OrderStatus}");

            if (updatedOrder.FilledQuantity > 0)
            {
                System.Console.WriteLine($"✅ Order filled: {updatedOrder.FilledQuantity} shares @ ${updatedOrder.AverageFillPrice:F2}");
                System.Console.WriteLine($"💰 Total Value: ${updatedOrder.FilledQuantity * updatedOrder.AverageFillPrice:F2}");
            }

            System.Console.WriteLine();
            System.Console.WriteLine("✅ Paper trade test completed successfully!");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Error testing paper trade: {ex.Message}");
            if (ex.InnerException != null)
            {
                System.Console.WriteLine($"   Inner: {ex.InnerException.Message}");
            }
            System.Console.WriteLine($"🔍 Stack trace: {ex.StackTrace}");
        }
    }

    /// <summary>
    /// Tests the state restoration system
    /// </summary>
    public static async Task HandleTestStateRestorationAsync(IServiceProvider services)
    {
        System.Console.WriteLine("🔄 Testing State Restoration System...");
        System.Console.WriteLine();

        try
        {
            // Get StateFlushService from DI
            var stateFlushService = services.GetService<StateFlushService>();
            if (stateFlushService == null)
            {
                System.Console.WriteLine("❌ StateFlushService not found in DI container");
                return;
            }

            // Perform health check
            System.Console.WriteLine("🏥 Performing health check...");
            var healthCheck = await stateFlushService.PerformHealthCheckAsync();

            System.Console.WriteLine($"📁 Backup Directory Exists: {(healthCheck.BackupDirectoryExists ? "✅" : "❌")}");
            System.Console.WriteLine($"✏️ Backup Directory Writable: {(healthCheck.BackupDirectoryWritable ? "✅" : "❌")}");
            System.Console.WriteLine($"📄 Backup File Exists: {(healthCheck.BackupFileExists ? "✅" : "❌")}");

            if (healthCheck.BackupFileExists)
            {
                System.Console.WriteLine($"📊 Backup File Size: {healthCheck.BackupFileSize:N0} bytes");
                System.Console.WriteLine($"⏰ Backup File Age: {healthCheck.BackupFileAge.TotalHours:F1} hours");
                System.Console.WriteLine($"✅ Backup File Valid: {(healthCheck.BackupFileValid ? "✅" : "❌")}");

                if (healthCheck.BackupFileValid)
                {
                    System.Console.WriteLine($"🛑 Trailing Stops in Backup: {healthCheck.TrailingStopsCount}");
                    System.Console.WriteLine($"📊 Position States in Backup: {healthCheck.PositionStatesCount}");
                    System.Console.WriteLine($"🔄 Retry Items in Backup: {healthCheck.RetryItemsCount}");
                    System.Console.WriteLine($"📅 Snapshot Age: {healthCheck.SnapshotAge.TotalHours:F1} hours");
                }
            }

            System.Console.WriteLine($"🔗 Redis Connected: {(healthCheck.RedisConnected ? "✅" : "❌")}");

            if (healthCheck.RedisConnected)
            {
                System.Console.WriteLine($"🛑 Redis Trailing Stops: {healthCheck.RedisTrailingStopsCount}");
                System.Console.WriteLine($"📊 Redis Position States: {healthCheck.RedisPositionStatesCount}");
                System.Console.WriteLine($"🔄 Redis Retry Queue Length: {healthCheck.RedisRetryQueueLength}");
            }

            System.Console.WriteLine();
            System.Console.WriteLine($"🎯 Overall Health: {(healthCheck.OverallHealthy ? "✅ HEALTHY" : "❌ UNHEALTHY")}");

            if (!string.IsNullOrEmpty(healthCheck.HealthCheckError))
            {
                System.Console.WriteLine($"❌ Health Check Error: {healthCheck.HealthCheckError}");
            }

            // Test state restoration if backup file exists
            if (healthCheck.BackupFileExists && healthCheck.BackupFileValid)
            {
                System.Console.WriteLine();
                System.Console.WriteLine("🧪 Testing state restoration process...");

                try
                {
                    using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
                    await stateFlushService.RestoreStateAsync(cts.Token);
                    System.Console.WriteLine("✅ State restoration test completed successfully");
                }
                catch (Exception ex)
                {
                    System.Console.WriteLine($"❌ State restoration test failed: {ex.Message}");
                }
            }
            else
            {
                System.Console.WriteLine();
                System.Console.WriteLine("ℹ️ No valid backup file found - state restoration test skipped");
                System.Console.WriteLine("💡 This is normal if the system hasn't created any state backups yet");
            }

            System.Console.WriteLine();
            System.Console.WriteLine("✅ State restoration system test completed");
        }
        catch (Exception ex)
        {
            System.Console.WriteLine($"❌ Test failed with exception: {ex.Message}");
            System.Console.WriteLine($"🔍 Exception type: {ex.GetType().Name}");
            if (ex.InnerException != null)
            {
                System.Console.WriteLine($"🔍 Inner exception: {ex.InnerException.Message}");
            }
        }
    }

    /// <summary>
    /// Mock system health service for lightweight command execution
    /// </summary>
    public class MockSystemHealthService : ISystemHealthService
    {
        public SystemHealthStatus GetCurrentStatus() => SystemHealthStatus.Healthy;

        public Task<HealthReport> GetHealthReportAsync()
        {
            var checks = new Dictionary<string, Services.HealthCheckResult>
            {
                ["System"] = new Services.HealthCheckResult("System", HealthCheckStatus.Healthy, "Mock health check", TimeSpan.FromMilliseconds(1), DateTime.UtcNow)
            };

            var report = new HealthReport(
                SystemHealthStatus.Healthy,
                checks,
                new List<HealthEvent>(),
                DateTime.UtcNow
            );

            return Task.FromResult(report);
        }

        public Task<bool> PerformHealthCheckAsync() => Task.FromResult(true);

        public List<HealthEvent> GetRecentEvents(int count = 20) => new List<HealthEvent>();

        public event EventHandler<HealthStatusChangedEventArgs>? HealthStatusChanged
        {
            add { /* Mock implementation - no actual event handling needed */ }
            remove { /* Mock implementation - no actual event handling needed */ }
        }

        public event EventHandler<HealthEventOccurredEventArgs>? HealthEventOccurred
        {
            add { /* Mock implementation - no actual event handling needed */ }
            remove { /* Mock implementation - no actual event handling needed */ }
        }
    }

    /// <summary>
    /// Mock trading metrics service for lightweight command execution
    /// </summary>
    public class MockTradingMetricsService : ITradingMetricsService
    {
        public Task RecordPerformanceAsync(string operation, TimeSpan duration, bool success, string? details = null) => Task.CompletedTask;
        public Task RecordTradeAsync(string symbol, decimal entryPrice, decimal? exitPrice, decimal quantity, string action, decimal pnl) => Task.CompletedTask;
        public Task RecordSignalAsync(string symbol, string signalType, decimal confidence, bool executed) => Task.CompletedTask;
        public Task RecordSystemMetricAsync(string metricName, decimal value, string unit) => Task.CompletedTask;

        public Task<TradingStatistics> GetTradingStatisticsAsync() => Task.FromResult(new TradingStatistics(
            TotalTrades: 0,
            ProfitableTrades: 0,
            TotalPnL: 0,
            WinRate: 0,
            TotalSignals: 0,
            ExecutedSignals: 0,
            SignalExecutionRate: 0,
            AverageConfidence: 0,
            MaxDrawdown: 0,
            SharpeRatio: 0,
            AverageWin: 0,
            AverageLoss: 0,
            GeneratedAt: DateTime.UtcNow
        ));

        public Dictionary<string, PerformanceMetric> GetPerformanceMetrics() => new Dictionary<string, PerformanceMetric>();
        public List<SystemMetric> GetRecentSystemMetrics(int count = 100) => new List<SystemMetric>();
        public Dictionary<string, decimal> GetKPIs() => new Dictionary<string, decimal>();
        public Task<string> ExportMetricsAsync() => Task.FromResult("{}");
    }

    /// <summary>
    /// Mock live state store for lightweight command execution
    /// </summary>
    public class MockLiveStateStore : ILiveStateStore
    {
        public Task SetTrailingStopAsync(string symbol, decimal stopPrice, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<decimal?> GetTrailingStopAsync(string symbol, CancellationToken cancellationToken = default) => Task.FromResult<decimal?>(null);
        public Task RemoveTrailingStopAsync(string symbol, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<Dictionary<string, decimal>> GetAllTrailingStopsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new Dictionary<string, decimal>());

        public Task SetPositionStateAsync(string symbol, PositionState state, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<PositionState?> GetPositionStateAsync(string symbol, CancellationToken cancellationToken = default) => Task.FromResult<PositionState?>(null);
        public Task RemovePositionStateAsync(string symbol, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<Dictionary<string, PositionState>> GetAllPositionStatesAsync(CancellationToken cancellationToken = default) => Task.FromResult(new Dictionary<string, PositionState>());

        public Task EnqueueRetryAsync(RetryItem item, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<RetryItem?> DequeueRetryAsync(CancellationToken cancellationToken = default) => Task.FromResult<RetryItem?>(null);
        public Task<List<RetryItem>> GetAllRetryItemsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new List<RetryItem>());
        public Task<int> GetRetryQueueLengthAsync(CancellationToken cancellationToken = default) => Task.FromResult(0);

        public Task FlagSignalAsync(string symbol, DateTime signalTime, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<bool> WasSignaledAsync(string symbol, DateTime signalTime, CancellationToken cancellationToken = default) => Task.FromResult(false);
        public Task ClearSignalFlagsAsync(DateTime cutoffTime, CancellationToken cancellationToken = default) => Task.CompletedTask;

        public Task SetMarketStateAsync(string key, object value, TimeSpan expiry, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<T?> GetMarketStateAsync<T>(string key, CancellationToken cancellationToken = default) where T : class => Task.FromResult<T?>(default);
        public Task RemoveMarketStateAsync(string key, CancellationToken cancellationToken = default) => Task.CompletedTask;

        public Task<bool> IsHealthyAsync(CancellationToken cancellationToken = default) => Task.FromResult(true);
        public Task FlushAllAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;

        public Task<LiveStateStats> GetStatsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new LiveStateStats(
            TrailingStopCount: 0,
            SignalFlagCount: 0,
            PositionStateCount: 0,
            RetryQueueLength: 0,
            MarketStateCacheCount: 0,
            LastHealthCheck: DateTime.UtcNow
        ));
    }

    /// <summary>
    /// Mock bar store for lightweight command execution
    /// </summary>
    public class MockBarStore : IBarStore
    {
        public Task SaveBarAsync(string symbol, string timeFrame, IBar bar, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task SaveBarsAsync(string symbol, string timeFrame, IEnumerable<IBar> bars, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<List<IBar>> LoadBarsAsync(string symbol, string timeFrame, DateTime from, DateTime to, CancellationToken cancellationToken = default) => Task.FromResult(new List<IBar>());
        public Task<DateTime?> GetLatestCachedDateAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default) => Task.FromResult<DateTime?>(null);
        public Task<DateTime?> GetEarliestCachedDateAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default) => Task.FromResult<DateTime?>(null);
        public Task<bool> HasBarsAsync(string symbol, string timeFrame, DateTime from, DateTime to, CancellationToken cancellationToken = default) => Task.FromResult(false);
        public Task<int> GetBarCountAsync(string symbol, string timeFrame, CancellationToken cancellationToken = default) => Task.FromResult(0);
        public Task CleanupOldBarsAsync(DateTime olderThan, CancellationToken cancellationToken = default) => Task.CompletedTask;
        public Task<BarStoreStats> GetStatsAsync(CancellationToken cancellationToken = default) => Task.FromResult(new BarStoreStats(0, 0, null, null, 0, new Dictionary<string, int>()));
        public Task InitializeAsync(CancellationToken cancellationToken = default) => Task.CompletedTask;
    }
}

/// <summary>
/// Simple test implementation of IBar for testing
/// </summary>
public record TestBar(string Symbol, DateTime TimeUtc, decimal Open, decimal High, decimal Low, decimal Close, decimal Volume) : IBar
{
    public decimal Vwap => (High + Low + Close) / 3;
    public ulong TradeCount => (ulong)(Volume / 100);
}
