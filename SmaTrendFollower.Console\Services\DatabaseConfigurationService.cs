using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Data.Sqlite;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for configuring database connections with optimized settings for high-performance caching
/// </summary>
public sealed class DatabaseConfigurationService : IDatabaseConfigurationService
{
    private readonly ILogger<DatabaseConfigurationService> _logger;
    private readonly IConfiguration _configuration;

    public DatabaseConfigurationService(ILogger<DatabaseConfigurationService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public void ConfigurePostgreSQL(DbContextOptionsBuilder options, string connectionString)
    {
        options.UseNpgsql(connectionString, npgsqlOptions =>
        {
            // Enable command timeout for long-running operations
            npgsqlOptions.CommandTimeout(30);
            // Enable retry on failure for better reliability
            npgsqlOptions.EnableRetryOnFailure(maxRetryCount: 3, maxRetryDelay: TimeSpan.FromSeconds(5), errorCodesToAdd: null);
        })
        .EnableSensitiveDataLogging(false) // Disable in production
        .EnableServiceProviderCaching(true) // Cache service provider for better performance
        .EnableDetailedErrors(false) // Disable in production for performance
        .LogTo(message => _logger.LogDebug("EF Core: {Message}", message), LogLevel.Debug);

        // Configure query behavior for performance
        options.ConfigureWarnings(warnings =>
        {
            warnings.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.RowLimitingOperationWithoutOrderByWarning);
        });
    }

    public string GetOptimizedPostgreSQLConnectionString(string baseConnectionString)
    {
        var builder = new Npgsql.NpgsqlConnectionStringBuilder(baseConnectionString)
        {
            // Performance optimizations for PostgreSQL
            CommandTimeout = 45, // Increased from 30 to handle network delays
            Timeout = 45,        // Increased from 30 to handle network delays
            ConnectionIdleLifetime = 300, // 5 minutes
            MaxPoolSize = 200, // Increased from 100 to handle high concurrency trading system
            MinPoolSize = 20,  // Increased from 10 to maintain warm connections
            Pooling = true,
            // Enable connection multiplexing for better performance
            Multiplexing = true,
            // Optimize for high-throughput scenarios
            ReadBufferSize = 8192,
            WriteBufferSize = 8192,
            // Network resilience settings to prevent "Exception while reading from stream"
            TcpKeepAlive = true,
            TcpKeepAliveTime = 60,     // Send keepalive every 60 seconds
            TcpKeepAliveInterval = 10,  // Retry every 10 seconds
            // Additional resilience
            CancellationTimeout = 45000 // 45 seconds in milliseconds
        };

        return builder.ConnectionString;
    }

    /// <summary>
    /// Creates an optimized PostgreSQL connection string builder with high-concurrency settings
    /// This is the standard configuration used throughout the trading system
    /// </summary>
    /// <param name="baseConnectionString">Base connection string</param>
    /// <returns>Optimized connection string builder</returns>
    public static Npgsql.NpgsqlConnectionStringBuilder CreateOptimizedBuilder(string baseConnectionString)
    {
        return new Npgsql.NpgsqlConnectionStringBuilder(baseConnectionString)
        {
            MaxPoolSize = 200, // Increased from default 100 to handle high concurrency
            MinPoolSize = 20,  // Increased from default 1 to maintain warm connections
            Pooling = true,
            ConnectionIdleLifetime = 300, // 5 minutes
            CommandTimeout = 45, // Increased from 30 to handle network delays
            Timeout = 45,        // Increased from 30 to handle network delays
            Multiplexing = true,
            ReadBufferSize = 8192,
            WriteBufferSize = 8192,
            // Network resilience settings
            TcpKeepAlive = true,
            TcpKeepAliveTime = 60,     // Send keepalive every 60 seconds
            TcpKeepAliveInterval = 10,  // Retry every 10 seconds
            // Additional resilience
            CancellationTimeout = 45000 // 45 seconds in milliseconds
        };
    }

    public async Task OptimizePostgreSQLAsync(string connectionString)
    {
        try
        {
            _logger.LogInformation("Starting PostgreSQL optimization");

            using var connection = new Npgsql.NpgsqlConnection(connectionString);
            await connection.OpenAsync();

            // Execute PostgreSQL optimization commands
            var optimizationCommands = new[]
            {
                "ANALYZE;", // Update table statistics for query optimizer
                "VACUUM ANALYZE;", // Reclaim space and update statistics
            };

            foreach (var command in optimizationCommands)
            {
                try
                {
                    using var cmd = new Npgsql.NpgsqlCommand(command, connection);
                    await cmd.ExecuteNonQueryAsync();
                    _logger.LogDebug("Executed PostgreSQL optimization command: {Command}", command);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to execute PostgreSQL optimization command: {Command}", command);
                }
            }

            _logger.LogInformation("PostgreSQL optimization completed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during PostgreSQL optimization");
            throw;
        }
    }
}
