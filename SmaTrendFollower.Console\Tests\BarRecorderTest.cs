using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using Alpaca.Markets;

namespace SmaTrendFollower.Tests;

/// <summary>
/// Test class for BarRecorder functionality
/// </summary>
public class BarRecorderTest
{
    /// <summary>
    /// Simple test to verify bar recording works
    /// </summary>
    public static async Task TestBarRecording()
    {
        var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
        var logger = loggerFactory.CreateLogger<BarRecorder>();
        var circuitBreakerLogger = loggerFactory.CreateLogger<CompressionCircuitBreakerService>();
        var compressionCircuitBreaker = new CompressionCircuitBreakerService(circuitBreakerLogger);

        var barRecorder = new BarRecorder(logger, compressionCircuitBreaker);
        
        // Create test bars
        var testBars = new List<TestBar>
        {
            new TestBar("AAPL", DateTime.UtcNow.Date, 150.00m, 152.00m, 149.00m, 151.00m, 1000000),
            new TestBar("AAPL", DateTime.UtcNow.Date.AddMinutes(1), 151.00m, 153.00m, 150.50m, 152.50m, 1200000),
            new TestBar("AAPL", DateTime.UtcNow.Date.AddMinutes(2), 152.50m, 154.00m, 152.00m, 153.75m, 900000)
        };
        
        System.Console.WriteLine("Recording test bars...");
        barRecorder.Record(testBars, "AAPL", "Minute");

        // Wait for compression to complete
        await Task.Delay(2000);

        System.Console.WriteLine("Bar recording test completed. Check Data/Bars directory for output files.");
    }
}

/// <summary>
/// Simple test implementation of IBar for testing
/// </summary>
public record TestBar(string Symbol, DateTime TimeUtc, decimal Open, decimal High, decimal Low, decimal Close, decimal Volume) : IBar
{
    public decimal Vwap => (High + Low + Close) / 3;
    public ulong TradeCount => (ulong)(Volume / 100);
}
